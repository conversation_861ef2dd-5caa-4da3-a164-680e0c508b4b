#!/usr/bin/env node

/**
 * Setup Telegram Bot Commands
 * 
 * This script sets up the bot commands via the Telegram Bot API
 * to improve user experience and discoverability.
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const TELEGRAM_API_BASE = `https://api.telegram.org/bot${BOT_TOKEN}`;

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(color, message, prefix = '') {
  console.log(`${colors[color]}${prefix}${message}${colors.reset}`);
}

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const url = `${TELEGRAM_API_BASE}/${endpoint}`;
    const postData = options.data ? JSON.stringify(options.data) : null;
    
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BuddyChip-Bot-Setup/1.0',
      }
    };

    if (postData) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: parsed,
            headers: res.headers
          });
        } catch (e) {
          reject(new Error(`Failed to parse JSON: ${data}`));
        }
      });
    });

    req.on('error', reject);
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

/**
 * Bot commands configuration
 */
const BOT_COMMANDS = [
  {
    command: 'start',
    description: '🚀 Welcome message and setup guide'
  },
  {
    command: 'help',
    description: '📚 Show all available commands and features'
  },
  {
    command: 'settings',
    description: '⚙️ Account settings and preferences'
  },
  {
    command: 'status',
    description: '📊 Check usage limits and account status'
  },
  {
    command: 'create',
    description: '📝 Generate original social media posts'
  }
];

/**
 * Set up bot commands
 */
async function setupBotCommands() {
  log('blue', '\nℹ️  Setting up bot commands...');
  
  try {
    const response = await makeRequest('setMyCommands', {
      data: { commands: BOT_COMMANDS }
    });
    
    if (response.data.ok) {
      log('green', '✅ Bot commands set successfully!');
      
      console.log('\n📋 Configured Commands:');
      BOT_COMMANDS.forEach(cmd => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
      
      return true;
    } else {
      log('red', `❌ Failed to set commands: ${response.data.description}`);
      return false;
    }
    
  } catch (err) {
    log('red', `❌ Error setting commands: ${err.message}`);
    return false;
  }
}

/**
 * Verify commands were set correctly
 */
async function verifyBotCommands() {
  log('blue', '\nℹ️  Verifying bot commands...');
  
  try {
    const response = await makeRequest('getMyCommands');
    
    if (response.data.ok) {
      const commands = response.data.result;
      
      if (commands.length === BOT_COMMANDS.length) {
        log('green', '✅ All commands verified successfully!');
        
        // Check if commands match
        let allMatch = true;
        for (let i = 0; i < BOT_COMMANDS.length; i++) {
          const expected = BOT_COMMANDS[i];
          const actual = commands.find(cmd => cmd.command === expected.command);
          
          if (!actual || actual.description !== expected.description) {
            allMatch = false;
            break;
          }
        }
        
        if (allMatch) {
          log('green', '✅ Command descriptions match perfectly!');
        } else {
          log('yellow', '⚠️  Some command descriptions may differ');
        }
        
      } else {
        log('yellow', `⚠️  Expected ${BOT_COMMANDS.length} commands, found ${commands.length}`);
      }
      
      console.log('\n📋 Current Commands:');
      commands.forEach(cmd => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
      
      return true;
    } else {
      log('red', `❌ Failed to get commands: ${response.data.description}`);
      return false;
    }
    
  } catch (err) {
    log('red', `❌ Error verifying commands: ${err.message}`);
    return false;
  }
}

/**
 * Set bot description
 */
async function setBotDescription() {
  log('blue', '\nℹ️  Setting bot description...');
  
  const description = `🤖 Benji - Your AI-powered Twitter assistant

I help you analyze Twitter content, generate smart replies, and provide real-time information with advanced search capabilities.

Features:
• 🐦 Twitter link analysis & reply generation
• 🔍 Real-time web search & research
• 🎨 AI image generation
• 💬 Intelligent conversations
• 📊 Crypto market insights

Link your BuddyChip account to unlock all features!`;

  try {
    const response = await makeRequest('setMyDescription', {
      data: { description }
    });
    
    if (response.data.ok) {
      log('green', '✅ Bot description set successfully!');
      return true;
    } else {
      log('red', `❌ Failed to set description: ${response.data.description}`);
      return false;
    }
    
  } catch (err) {
    log('red', `❌ Error setting description: ${err.message}`);
    return false;
  }
}

/**
 * Set bot short description
 */
async function setBotShortDescription() {
  log('blue', '\nℹ️  Setting bot short description...');
  
  const shortDescription = 'AI-powered Twitter assistant with smart reply generation and real-time search capabilities';
  
  try {
    const response = await makeRequest('setMyShortDescription', {
      data: { short_description: shortDescription }
    });
    
    if (response.data.ok) {
      log('green', '✅ Bot short description set successfully!');
      return true;
    } else {
      log('red', `❌ Failed to set short description: ${response.data.description}`);
      return false;
    }
    
  } catch (err) {
    log('red', `❌ Error setting short description: ${err.message}`);
    return false;
  }
}

/**
 * Test final bot configuration
 */
async function testFinalConfiguration() {
  log('blue', '\nℹ️  Testing final bot configuration...');
  
  try {
    const response = await makeRequest('getMe');
    
    if (response.data.ok) {
      const bot = response.data.result;
      
      log('green', '✅ Bot configuration test successful!');
      
      console.log('\n🤖 Final Bot Configuration:');
      console.log(`   Name: ${bot.first_name}`);
      console.log(`   Username: @${bot.username}`);
      console.log(`   ID: ${bot.id}`);
      console.log(`   Can join groups: ${bot.can_join_groups ? 'Yes' : 'No'}`);
      console.log(`   Supports inline: ${bot.supports_inline_queries ? 'Yes' : 'No'}`);
      
      if (bot.description) {
        console.log(`   Description: ${bot.description.substring(0, 100)}...`);
      }
      
      if (bot.short_description) {
        console.log(`   Short description: ${bot.short_description}`);
      }
      
      return bot;
    } else {
      log('red', `❌ Configuration test failed: ${response.data.description}`);
      return null;
    }
    
  } catch (err) {
    log('red', `❌ Error testing configuration: ${err.message}`);
    return null;
  }
}

/**
 * Generate setup report
 */
async function generateSetupReport(botInfo) {
  log('cyan', '\n📋 BOT SETUP SUMMARY');
  log('cyan', '====================\n');
  
  if (botInfo) {
    log('green', '✅ Bot setup completed successfully!');
    
    console.log('\n🔧 Configuration Applied:');
    console.log('   ✅ Commands configured');
    console.log('   ✅ Description set');
    console.log('   ✅ Short description set');
    console.log('   ✅ Bot verified and operational');
    
    console.log('\n🌐 Bot Information:');
    console.log(`   Profile: https://t.me/${botInfo.username}`);
    console.log(`   Username: @${botInfo.username}`);
    console.log(`   Bot ID: ${botInfo.id}`);
    
    console.log('\n📱 Ready for Testing:');
    console.log('   1. Open bot in Telegram');
    console.log('   2. Try /start command');
    console.log('   3. Test Twitter URL processing');
    console.log('   4. Check command menu in Telegram');
    
  } else {
    log('red', '❌ Bot setup incomplete - some configurations failed');
  }
  
  console.log('\n🔧 Manual Setup (via @BotFather):');
  console.log('   • /setcommands - Already done by this script');
  console.log('   • /setdescription - Already done by this script');
  console.log('   • /setabouttext - Already done by this script');
  console.log('   • /setuserpic - Set a profile picture');
  console.log('   • /setinline - Enable inline mode (optional)');
}

async function main() {
  console.log(`${colors.cyan}╔════════════════════════════════════════╗`);
  console.log(`║        TELEGRAM BOT SETUP SCRIPT      ║`);
  console.log(`╚════════════════════════════════════════╝${colors.reset}`);
  
  log('blue', '\n🤖 Setting up BuddyChip Telegram Bot...');
  
  // Setup sequence
  const commandsSet = await setupBotCommands();
  const commandsVerified = await verifyBotCommands();
  const descriptionSet = await setBotDescription();
  const shortDescSet = await setBotShortDescription();
  const botInfo = await testFinalConfiguration();
  
  await generateSetupReport(botInfo);
  
  if (commandsSet && commandsVerified && descriptionSet && shortDescSet && botInfo) {
    log('green', '\n✅ Bot setup completed successfully!\n');
    process.exit(0);
  } else {
    log('yellow', '\n⚠️  Bot setup completed with some warnings\n');
    process.exit(0);
  }
}

if (require.main === module) {
  main().catch(err => {
    log('red', `❌ Fatal error: ${err.message}`);
    process.exit(1);
  });
}