/**
 * Benji AI Agent - Core Implementation
 *
 * The main AI agent that orchestrates all tools and generates responses
 */

import { generateId, streamText } from "ai";
import { getModel, getModelByPlan, type ModelName } from "./ai-providers";
import { cookieClient } from "./cookie-client";
import { prisma } from "./db-utils";
import mem0Service from "./mem0-service";
import { PERSONA_MEMORY_TYPES } from "./twitter-persona-service";
import { exaSearchTool } from "./tools/exa-search";
import { mem0MemoryTool } from "./tools/mem0-memory";
import { imageGenerationTool } from "./tools/openai-image";
import { streamingImageGenerationTool, multiTurnImageEditTool } from "./tools/openai-image-streaming";
import { xaiSearchTool } from "./tools/xai-search";

export interface BenjiConfig {
  model?: ModelName;
  userId?: string;
  userPlan?: string;
  maxTokens?: number;
  temperature?: number;
  enableTools?: boolean;
  maxSteps?: number;
  personalityPrompt?: string;
  customSystemPrompt?: string;
  useFirstPerson?: boolean;
}

export interface BenjiContext {
  mentionId?: string;
  mentionContent?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  // New fields for enhanced context
  monitoredAccountInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  mentionAuthorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  conversationHistory?: Array<{
    role: "user" | "assistant";
    content: string;
  }>;
  // Memory context from mem0
  memoryContext?: string;
  // Persona-specific memory context
  personaMemoryContext?: string;
  sessionId?: string;
  // Market intelligence context
  marketIntelligence?: {
    trendingProjects?: Array<{
      name: string;
      symbol?: string;
      sector?: string;
      mindshare?: number;
      mindshareDelta?: number;
    }>;
    relevantProjects?: Array<{
      name: string;
      symbol?: string;
      sector?: string;
      mindshare?: number;
      mindshareDelta?: number;
    }>;
    sector?: string;
    marketSentiment?: "bullish" | "bearish" | "neutral";
    updatedAt?: string;
  };
}

export class BenjiAgent {
  private config: BenjiConfig;
  private marketIntelligenceCache: Map<
    string,
    { data: any; timestamp: number }
  > = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

  constructor(config: BenjiConfig = {}) {
    this.config = {
      model: config.model || "gemini25Flash",
      userId: config.userId,
      userPlan: config.userPlan || "reply-guy",
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      enableTools: config.enableTools ?? true,
      maxSteps: config.maxSteps || 5,
      personalityPrompt: config.personalityPrompt,
      customSystemPrompt: config.customSystemPrompt,
      useFirstPerson: config.useFirstPerson ?? true,
    };

    // Auto-select model based on user plan if not specified
    if (!config.model && config.userPlan) {
      this.config.model = getModelByPlan(config.userPlan);
    }
  }

  /**
   * Generate a response for a Twitter mention
   */
  async generateMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt("mention", context);

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Please analyze this tweet and generate an appropriate response:\n\n"${mentionContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate a response for any tweet (Quick Reply feature)
   */
  async generateQuickReply(tweetContent: string, context: BenjiContext = {}) {
    const systemPrompt = this.buildSystemPrompt("quick-reply", context);

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Generate a thoughtful response to this tweet:\n\n"${tweetContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate an original post/tweet based on user prompt
   */
  async generatePost(userPrompt: string, context: BenjiContext = {}) {
    console.log("📝 Benji: Generating original post for prompt:", userPrompt.substring(0, 100) + "...");

    const systemPrompt = this.buildSystemPrompt("post", context);

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Create an engaging social media post based on this prompt:\n\n"${userPrompt}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate an enhanced response for a Twitter mention with tool assistance
   * Always uses OpenAI o3 model for highest quality responses (with fallback)
   */
  async generateEnhancedMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    console.log(
      "🧠 Benji: Enhanced response - using OpenAI o3 via OpenRouter for maximum quality"
    );

    const systemPrompt = this.buildSystemPrompt("enhanced", context);

    let userPrompt = `Please respond to this tweet:\n\n"${mentionContent}"`;

    // Add context about who mentioned whom
    if (context.mentionAuthorInfo && context.monitoredAccountInfo) {
      userPrompt += `\n\nContext: @${context.mentionAuthorInfo.handle} mentioned @${context.monitoredAccountInfo.handle}.`;
    }

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: userPrompt,
      },
    ];

    // Try o3 first, fallback to Gemini Pro if o3 fails
    try {
      console.log("🎯 Benji: Attempting OpenAI o3 via OpenRouter...");
      return await this.streamResponseWithModel(messages, context, "openaiO3");
    } catch (error) {
      console.warn(
        "⚠️ Benji: o3 model failed, falling back to Gemini Pro:",
        error
      );
      console.warn("🔍 Benji: o3 error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        name: error instanceof Error ? error.name : "Unknown",
        stack: error instanceof Error ? error.stack?.split("\n")[0] : undefined,
      });
      console.log("🔄 Benji: Using Gemini Pro as enhanced fallback...");
      return await this.streamResponseWithModel(
        messages,
        context,
        "gemini25Pro"
      );
    }
  }

  /**
   * Calculate bullish score for a tweet
   */
  async calculateBullishScore(content: string): Promise<number> {
    const model = getModel(this.config.model!);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a sentiment analysis expert. Analyze the sentiment and positivity of tweets and return a "bullish score" from 1-100 where:
          - 1-20: Very negative, bearish, pessimistic
          - 21-40: Somewhat negative, skeptical
          - 41-60: Neutral, mixed sentiment
          - 61-80: Positive, optimistic
          - 81-100: Very positive, bullish, enthusiastic
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: "user",
          content: `Analyze this tweet and give it a bullish score: "${content}"`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    const score = parseInt(text.trim().replace(/\D/g, ""));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Calculate importance score for a tweet
   */
  async calculateImportanceScore(
    content: string,
    authorInfo?: {
      followers?: number;
      verified?: boolean;
      handle?: string;
    }
  ): Promise<number> {
    const model = getModel(this.config.model!);

    // Build context about the author if available
    let authorContext = "";
    if (authorInfo) {
      const followerText = authorInfo.followers
        ? ` with ${authorInfo.followers.toLocaleString()} followers`
        : "";
      const verifiedText = authorInfo.verified ? " (verified account)" : "";
      authorContext = `\nAuthor: @${authorInfo.handle || "unknown"}${followerText}${verifiedText}`;
    }

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a social media engagement expert. Analyze tweets and return an "importance score" from 1-100 where:
          - 1-20: Spam, low-value, irrelevant content
          - 21-40: Personal posts, casual mentions with little engagement potential
          - 41-60: Standard social media content, moderate engagement potential
          - 61-80: Valuable content, questions, industry insights, good engagement opportunity
          - 81-100: High-value content, trending topics, influential discussions, urgent responses needed
          
          Consider factors like:
          - Content depth and value
          - Engagement potential (questions, requests, discussions)
          - Author influence and follower count
          - Relevance to business/professional interests
          - Urgency of response needed
          - Opportunity for meaningful conversation
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: "user",
          content: `Analyze this tweet and give it an importance score: "${content}"${authorContext}`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    const score = parseInt(text.trim().replace(/\D/g, ""));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Extract AI-enhanced keywords from tweet content
   */
  async extractEnhancedKeywords(content: string): Promise<string[]> {
    const model = getModel(this.config.model!);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a content analysis expert. Extract the most important keywords and topics from tweets for categorization and search.
          
          Focus on:
          - Main topics and subjects
          - Industry terms and technical concepts
          - Product names and brand mentions
          - Actionable items and intents
          - Emotional context keywords
          
          Return 3-7 keywords maximum, separated by commas.
          Exclude common words like: the, and, or, but, this, that, have, will, would.
          Prioritize specific, meaningful terms that capture the tweet's essence.
          
          Example: "AI, machine learning, startup, innovation, feedback"`,
        },
        {
          role: "user",
          content: `Extract keywords from this tweet: "${content}"`,
        },
      ],
      maxTokens: 50,
      temperature: 0.3,
    });

    // Extract keywords from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    // Parse comma-separated keywords and clean them
    const keywords = text
      .split(",")
      .map((keyword) => keyword.trim().toLowerCase())
      .filter((keyword) => keyword.length > 2 && keyword.length < 30)
      .slice(0, 7); // Limit to 7 keywords max

    return keywords.length > 0 ? keywords : ["general"]; // Fallback if no keywords extracted
  }

  /**
   * Perform comprehensive AI analysis of a tweet
   */
  async performFullAnalysis(
    content: string,
    authorInfo?: {
      name?: string;
      handle?: string;
      followers?: number;
      verified?: boolean;
      avatarUrl?: string;
    }
  ): Promise<{
    bullishScore: number;
    importanceScore: number;
    keywords: string[];
    analysisData: {
      sentiment: string;
      priority: string;
      recommendedAction: string;
      confidence: number;
      processingTime: number;
    };
  }> {
    const startTime = Date.now();

    try {
      console.log(
        "🔍 Benji: Starting full analysis for tweet content:",
        content.substring(0, 100) + "..."
      );

      // Run analysis in parallel for better performance
      const [bullishScore, importanceScore, keywords] = await Promise.all([
        this.calculateBullishScore(content),
        this.calculateImportanceScore(content, authorInfo),
        this.extractEnhancedKeywords(content),
      ]);

      // Determine sentiment category based on bullish score
      let sentiment: string;
      if (bullishScore >= 81) sentiment = "very_positive";
      else if (bullishScore >= 61) sentiment = "positive";
      else if (bullishScore >= 41) sentiment = "neutral";
      else if (bullishScore >= 21) sentiment = "negative";
      else sentiment = "very_negative";

      // Determine priority based on importance score
      let priority: string;
      if (importanceScore >= 81) priority = "urgent";
      else if (importanceScore >= 61) priority = "high";
      else if (importanceScore >= 41) priority = "medium";
      else if (importanceScore >= 21) priority = "low";
      else priority = "ignore";

      // Determine recommended action
      let recommendedAction: string;
      if (importanceScore >= 81) {
        recommendedAction = "respond_immediately";
      } else if (importanceScore >= 61 && bullishScore >= 61) {
        recommendedAction = "engage_positively";
      } else if (importanceScore >= 61 && bullishScore <= 40) {
        recommendedAction = "address_concerns";
      } else if (importanceScore >= 41) {
        recommendedAction = "monitor_and_consider";
      } else {
        recommendedAction = "archive_or_ignore";
      }

      // Calculate confidence based on content length and author info availability
      let confidence = 0.7; // Base confidence
      if (content.length > 50) confidence += 0.1; // Longer content = more context
      if (content.length > 100) confidence += 0.1;
      if (authorInfo?.followers && authorInfo.followers > 1000)
        confidence += 0.05;
      if (authorInfo?.verified) confidence += 0.05;
      confidence = Math.min(0.95, confidence); // Cap at 95%

      const processingTime = Date.now() - startTime;

      console.log("✅ Benji: Full analysis completed:", {
        bullishScore,
        importanceScore,
        keywordCount: keywords.length,
        sentiment,
        priority,
        processingTime,
      });

      return {
        bullishScore,
        importanceScore,
        keywords,
        analysisData: {
          sentiment,
          priority,
          recommendedAction,
          confidence,
          processingTime,
        },
      };
    } catch (error) {
      console.error("❌ Benji: Full analysis failed:", error);

      // Return fallback analysis if AI fails
      const processingTime = Date.now() - startTime;
      return {
        bullishScore: 50,
        importanceScore: 50,
        keywords: ["general"],
        analysisData: {
          sentiment: "neutral",
          priority: "medium",
          recommendedAction: "monitor_and_consider",
          confidence: 0.3, // Low confidence for fallback
          processingTime,
        },
      };
    }
  }

  /**
   * Core streaming response method
   */
  private async streamResponse(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>,
    _context: BenjiContext = {}
  ) {
    return this.streamResponseWithModel(messages, _context, this.config.model!);
  }

  /**
   * Core streaming response method with specific model override
   */
  private async streamResponseWithModel(
    messages: Array<{ role: "system" | "user" | "assistant"; content: string }>,
    _context: BenjiContext = {},
    modelName: ModelName
  ) {
    console.log("🔄 Benji: Starting streamResponse with config:", {
      model: modelName,
      enableTools: this.config.enableTools,
      maxTokens: this.config.maxTokens,
      userId: this.config.userId,
    });

    try {
      const model = getModel(modelName);
      console.log("✅ Benji: Model loaded successfully:", modelName);

      const tools = this.config.enableTools
        ? {
            searchWeb: xaiSearchTool,
            searchKnowledge: exaSearchTool,
            generateImage: imageGenerationTool,
            generateImageStreaming: streamingImageGenerationTool,
            editImage: multiTurnImageEditTool,
          }
        : undefined;

      console.log(
        "🛠️ Benji: Tools configured:",
        this.config.enableTools ? "Enabled" : "Disabled"
      );

      // Special configuration for o3 model
      const isO3Model = modelName === "openaiO3";
      const streamConfig = {
        model,
        messages,
        tools: isO3Model ? undefined : tools, // o3 might not support tools initially
        maxTokens: isO3Model
          ? Math.min(this.config.maxTokens || 4000, 2000)
          : this.config.maxTokens, // o3 might have lower limits
        temperature: isO3Model ? 0.7 : this.config.temperature, // o3 might work better with specific temperature
        maxSteps: isO3Model ? 1 : this.config.maxSteps, // o3 might not support multi-step
      };

      console.log("🔧 Benji: streamText configuration:", {
        modelName,
        isO3Model,
        maxTokens: streamConfig.maxTokens,
        temperature: streamConfig.temperature,
        maxSteps: streamConfig.maxSteps,
        toolsEnabled: !!streamConfig.tools,
        messagesCount: messages.length,
      });

      const result = streamText(streamConfig);

      console.log("🚀 Benji: streamText call initiated successfully");
      return result;
    } catch (error) {
      console.error("❌ Benji Agent streamResponse error:", error);
      console.error("🔍 Benji: Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        config: this.config,
        requestedModel: modelName,
      });
      throw new Error(
        `AI generation failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Build system prompt based on context
   */
  private buildSystemPrompt(
    type: "mention" | "quick-reply" | "enhanced" | "post",
    context: BenjiContext
  ): string {
    const isFirstPerson = this.config.useFirstPerson ?? true;

    let prompt = `You are Benji, an AI assistant specialized in social media engagement.

CRITICAL RESPONSE RULES:`;

    if (type === "post") {
      prompt += `
- CREATE an original, engaging social media post based on the user's prompt
- RESPOND ONLY with the direct post content - NO explanations or context
- BE ENGAGING and authentic - make it worth reading and sharing
- NEVER include phrases like "Here's a post about..." or "This post discusses:"
- ONLY provide the actual post content that would be published
- Stay within 280 characters maximum for Twitter compatibility
- Make it compelling, valuable, and true to the personality`;
    } else {
      prompt += `
- RESPOND ONLY with the direct tweet reply - NO explanations or context
- BE ULTRA-CONCISE - every word must add value
- NEVER include phrases like "This tweet seems to be..." or "Here's a response:"
- ONLY provide content that is directly relevant to the original tweet
- Stay within 280 characters maximum`;
    }

    if (isFirstPerson) {
      prompt += `
- ALWAYS respond in FIRST PERSON as the account owner (use "I", "my", "me")
- NEVER refer to the account owner in third person or by name
- NEVER say things like "They consistently push boundaries" - you ARE the account owner
- Answer as if you are the account owner responding directly to the conversation`;
    } else {
      prompt += `
- Respond as an EXTERNAL USER who follows this account (use "you", "your", "they")
- Refer to the account owner appropriately (by name or handle when relevant)
- Respond as if you're a knowledgeable follower engaging with their content
- Show respect and appreciation for the account owner's work when appropriate`;
    }

    prompt += `

Your personality:
- Professional yet personable
- Knowledgeable and helpful
- Always concise and direct
- Respectful and positive

Guidelines:
- Match the tone of the original tweet
- Provide immediate value (insights, questions, resources)
- Be conversational and authentic
- Avoid promotional language
- Use tools only when they add specific value to the response`;

    // Add personality prompt if available
    if (this.config.personalityPrompt) {
      prompt += `\n\nPersonality Style: ${this.config.personalityPrompt}`;
    }

    // Add custom system prompt if available
    if (this.config.customSystemPrompt) {
      prompt += `\n\nAdditional Instructions: ${this.config.customSystemPrompt}`;
    }

    // Add market intelligence context if available
    if (context.marketIntelligence) {
      const mi = context.marketIntelligence;
      prompt += `\n\nCURRENT CRYPTO MARKET INTELLIGENCE:`;

      if (mi.sector) {
        prompt += `\n- Detected sector: ${mi.sector}`;
      }

      if (mi.trendingProjects && mi.trendingProjects.length > 0) {
        prompt += `\n- Trending projects (7-day mindshare):`;
        mi.trendingProjects.slice(0, 3).forEach((p) => {
          const delta =
            p.mindshareDelta !== undefined
              ? ` (${p.mindshareDelta > 0 ? "+" : ""}${p.mindshareDelta}%)`
              : "";
          prompt += `\n  • ${p.name}${p.symbol ? ` ($${p.symbol})` : ""}: ${p.mindshare}% mindshare${delta}`;
        });
      }

      if (mi.relevantProjects && mi.relevantProjects.length > 0) {
        prompt += `\n- Mentioned projects:`;
        mi.relevantProjects.slice(0, 2).forEach((p) => {
          const delta =
            p.mindshareDelta !== undefined
              ? ` (${p.mindshareDelta > 0 ? "+" : ""}${p.mindshareDelta}%)`
              : "";
          prompt += `\n  • ${p.name}${p.symbol ? ` ($${p.symbol})` : ""}: ${p.mindshare}% mindshare${delta}`;
        });
      }

      if (mi.marketSentiment) {
        prompt += `\n- Overall market sentiment: ${mi.marketSentiment}`;
      }

      prompt += `\n- Data updated: ${mi.updatedAt ? new Date(mi.updatedAt).toLocaleString() : "Unknown"}`;
      prompt += `\n\nUse this market context to provide informed, relevant responses about crypto topics.`;

      prompt += `\n- Data updated: ${mi.updatedAt || "recently"}`;
      prompt += `\n\nUse this market context to provide more informed, relevant responses when discussing crypto topics. Only reference this information when it adds value to the conversation.`;
    }

    // Add memory context if available
    if (context.memoryContext) {
      prompt += `\n\n## Previous Conversation Context
${context.memoryContext}

Use this context to provide more personalized and relevant responses based on previous interactions.`;
    }

    // Add persona-specific memory context if available
    if (context.personaMemoryContext) {
      prompt += `\n\n${context.personaMemoryContext}`;
    }

    // Add final instructions based on response mode
    if (isFirstPerson) {
      prompt += `\n\nRemember: You ARE the account owner. Respond in first person (I/my/me) and always answer in short. Never reference yourself in third person.`;
    } else {
      prompt += `\n\nRemember: You are an external user responding TO the account owner. Use second person (you/your) or third person (they/their) and always answer in short. Be respectful and engaging.`;
    }

    if (type === "mention") {
      return (
        prompt +
        `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`
      );
    }

    if (type === "enhanced") {
      let enhancedPrompt = `\n\nYou have access to search tools for additional context if needed. Use them only when they add genuine value to your response.`;

      // Add identity context if available
      if (context.monitoredAccountInfo) {
        if (isFirstPerson) {
          enhancedPrompt += `\n\nCRITICAL IDENTITY CONTEXT:
- YOU ARE ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is YOUR Twitter account that is being mentioned
- Respond AS YOURSELF, not as an AI assistant
- Use "I", "my", "me" - you ARE ${context.monitoredAccountInfo.name}
- NEVER address yourself in third person
- NEVER say "${context.monitoredAccountInfo.name} thinks..." - YOU are ${context.monitoredAccountInfo.name}`;
        } else {
          enhancedPrompt += `\n\nCRITICAL IDENTITY CONTEXT:
- You are responding TO ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is THEIR Twitter account that posted the content
- Respond AS AN EXTERNAL USER, not as the account owner
- Use "you", "your" when addressing them directly
- Use "they", "their" when referring to them in third person
- Show respect and appreciation for their work when appropriate`;
        }
      }

      if (isFirstPerson) {
        enhancedPrompt += `\n\nREMEMBER: You are responding AS the account owner, not TO them. Use first person always.`;
      } else {
        enhancedPrompt += `\n\nREMEMBER: You are responding TO the account owner as an external user. Use second/third person appropriately.`;
      }

      enhancedPrompt += `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`;

      return prompt + enhancedPrompt;
    }

    return (
      prompt +
      `\n\nGenerate a direct, authentic reply that adds meaningful value to the conversation.`
    );
  }

  /**
   * Fetch market intelligence context for crypto-related tweets
   */
  private async fetchMarketIntelligence(
    content: string
  ): Promise<BenjiContext["marketIntelligence"] | null> {
    try {
      // Create cache key based on content hash (simple approach)
      const cacheKey = Buffer.from(
        content.toLowerCase().replace(/\s+/g, " ").trim()
      )
        .toString("base64")
        .slice(0, 32);

      // Check cache first
      const cached = this.marketIntelligenceCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        console.log("🍪 Benji: Using cached market intelligence");
        return cached.data;
      }
      // Detect crypto-related keywords (expanded list)
      const cryptoKeywords = [
        "bitcoin",
        "btc",
        "ethereum",
        "eth",
        "crypto",
        "blockchain",
        "defi",
        "nft",
        "web3",
        "dao",
        "token",
        "coin",
        "altcoin",
        "trading",
        "hodl",
        "mining",
        "staking",
        "yield",
        "liquidity",
        "dex",
        "cex",
        "smart contract",
        "protocol",
        "layer",
        "chain",
        "wallet",
        "bull",
        "bear",
        "moon",
        "diamond hands",
        "ape",
        "solana",
        "sol",
        "cardano",
        "ada",
        "polkadot",
        "dot",
        "chainlink",
        "link",
        "uniswap",
        "uni",
        "aave",
        "compound",
        "maker",
        "mkr",
        "sushi",
        "pancake",
        "binance",
        "bnb",
        "polygon",
        "matic",
        "avalanche",
        "avax",
        "fantom",
        "ftm",
        "cosmos",
        "atom",
        "terra",
        "luna",
        "near",
        "flow",
        "tezos",
        "xtz",
        "algorand",
        "algo",
        "hedera",
        "hbar",
        "icp",
        "dfinity",
        "theta",
        "tfuel",
        "gala",
        "sand",
        "mana",
        "axs",
        "slp",
        "enjin",
        "enj",
        "chz",
        "chiliz",
      ];

      const contentLower = content.toLowerCase();
      const hasCryptoKeywords = cryptoKeywords.some(
        (keyword) =>
          contentLower.includes(keyword) || contentLower.includes(`$${keyword}`)
      );

      if (!hasCryptoKeywords) {
        console.log(
          "🍪 Benji: No crypto keywords detected, skipping market intelligence"
        );
        return null;
      }

      console.log(
        "🍪 Benji: Detected crypto-related content, fetching market intelligence..."
      );

      // Extract potential project mentions (look for $SYMBOL patterns and common names)
      const symbolMatches = content.match(/\$[A-Z]{2,10}/g);
      const projectSlugs =
        symbolMatches?.map((symbol) => symbol.substring(1).toLowerCase()) || [];

      // Also detect common project names mentioned without $ prefix
      const commonProjects = [
        "bitcoin",
        "ethereum",
        "solana",
        "cardano",
        "polkadot",
        "chainlink",
        "uniswap",
        "aave",
      ];
      const mentionedProjects = commonProjects.filter((project) =>
        contentLower.includes(project)
      );
      projectSlugs.push(...mentionedProjects);

      console.log("🔍 Benji: Detected project mentions:", projectSlugs);

      // Detect sector keywords (expanded)
      let detectedSector: string | undefined;
      const sectorKeywords = {
        defi: [
          "defi",
          "decentralized finance",
          "yield",
          "liquidity",
          "dex",
          "lending",
          "borrowing",
          "swap",
          "farm",
          "pool",
        ],
        gaming: [
          "gaming",
          "play to earn",
          "p2e",
          "nft game",
          "metaverse",
          "virtual world",
          "gamefi",
          "guild",
        ],
        infrastructure: [
          "infrastructure",
          "layer 1",
          "layer 2",
          "blockchain",
          "protocol",
          "consensus",
          "validator",
          "node",
        ],
        meme: [
          "meme",
          "doge",
          "shib",
          "pepe",
          "moon",
          "ape",
          "diamond hands",
          "hodl",
          "to the moon",
        ],
        ai: [
          "ai",
          "artificial intelligence",
          "machine learning",
          "neural",
          "compute",
          "gpu",
          "training",
        ],
        layer1: [
          "layer 1",
          "l1",
          "mainnet",
          "consensus",
          "validator",
          "staking",
        ],
        layer2: ["layer 2", "l2", "scaling", "rollup", "sidechain", "plasma"],
      };

      for (const [sector, keywords] of Object.entries(sectorKeywords)) {
        if (keywords.some((keyword) => contentLower.includes(keyword))) {
          detectedSector = sector;
          console.log("🎯 Benji: Detected sector:", sector);
          break;
        }
      }

      // Fetch market data (with error handling to not break response generation)
      let trendingProjects: any[] = [];
      let relevantProjects: any[] = [];

      try {
        console.log(
          "📊 Benji: Fetching trending projects for sector:",
          detectedSector || "general"
        );

        // Get trending projects in detected sector or general trending
        const trending = await cookieClient.getTrendingProjects(
          detectedSector,
          "_7Days"
        );
        trendingProjects = trending.slice(0, 5).map((project) => ({
          name: project.name,
          symbol: project.symbol,
          sector: project.sector,
          mindshare: project.mindshare,
          mindshareDelta: project.mindshareDelta,
        }));

        console.log(
          "✅ Benji: Fetched trending projects:",
          trendingProjects.length
        );

        // If we have specific project mentions, try to get their data
        if (projectSlugs.length > 0) {
          console.log(
            "🔍 Benji: Processing specific project mentions:",
            projectSlugs
          );

          try {
            // Try to get real project data from Cookie.fun API
            const projectSearchPromises = projectSlugs
              .slice(0, 3)
              .map(async (slug) => {
                try {
                  const searchResult = await cookieClient.searchProjects({
                    query: slug,
                    limit: 1,
                  });
                  if (searchResult.data && searchResult.data.length > 0) {
                    const project = searchResult.data[0];
                    return {
                      name: project.name,
                      symbol: project.symbol,
                      sector: project.sector,
                      mindshare: project.mindshare,
                      mindshareDelta: project.mindshareDelta,
                    };
                  }
                  return null;
                } catch (searchError) {
                  console.warn(
                    `⚠️ Benji: Failed to fetch data for project ${slug}:`,
                    searchError
                  );
                  return null;
                }
              });

            const projectResults = await Promise.all(projectSearchPromises);
            relevantProjects = projectResults.filter(Boolean);

            console.log(
              "✅ Benji: Fetched relevant project data:",
              relevantProjects.length
            );
          } catch (projectError) {
            console.warn(
              "⚠️ Benji: Failed to fetch specific project data, using fallback:",
              projectError
            );

            // Fallback to basic project info
            relevantProjects = projectSlugs.slice(0, 3).map((slug) => ({
              name: slug,
              symbol: slug.toUpperCase(),
              sector: detectedSector || "crypto",
              mindshare: 50,
              mindshareDelta: 0,
            }));
          }
        }
      } catch (error) {
        console.warn(
          "⚠️ Benji: Failed to fetch market data, continuing without it:",
          error
        );
        // Continue without market data rather than failing
      }

      // Determine market sentiment based on trending projects mindshare
      let marketSentiment: "bullish" | "bearish" | "neutral" = "neutral";
      if (trendingProjects.length > 0) {
        const avgMindshare =
          trendingProjects.reduce((sum, p) => sum + (p.mindshare || 0), 0) /
          trendingProjects.length;
        if (avgMindshare > 70) marketSentiment = "bullish";
        else if (avgMindshare < 30) marketSentiment = "bearish";
      }

      const marketIntelligence = {
        trendingProjects,
        relevantProjects,
        sector: detectedSector,
        marketSentiment,
        updatedAt: new Date().toISOString(),
      };

      // Cache the result
      this.marketIntelligenceCache.set(cacheKey, {
        data: marketIntelligence,
        timestamp: Date.now(),
      });

      // Clean old cache entries (keep only last 10)
      if (this.marketIntelligenceCache.size > 10) {
        const oldestKey = this.marketIntelligenceCache.keys().next().value;
        if (oldestKey) this.marketIntelligenceCache.delete(oldestKey);
      }

      console.log("✅ Benji: Market intelligence fetched and cached");
      return marketIntelligence;
    } catch (error) {
      console.warn(
        "⚠️ Benji: Failed to fetch market intelligence, continuing without it:",
        error
      );
      return null;
    }
  }

  /**
   * Enhanced mention response with market intelligence
   */
  async generateEnhancedMentionResponseWithIntelligence(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    console.log(
      "🚀 Benji: Starting enhanced response with market intelligence..."
    );

    // Fetch market intelligence if not already provided
    if (!context.marketIntelligence) {
      console.log("🍪 Benji: Fetching fresh market intelligence...");
      context.marketIntelligence =
        (await this.fetchMarketIntelligence(mentionContent)) || undefined;
    }

    // Log market intelligence status
    if (context.marketIntelligence) {
      console.log("✅ Benji: Market intelligence available:", {
        sector: context.marketIntelligence.sector,
        trendingCount: context.marketIntelligence.trendingProjects?.length || 0,
        relevantCount: context.marketIntelligence.relevantProjects?.length || 0,
        sentiment: context.marketIntelligence.marketSentiment,
      });
    } else {
      console.log("ℹ️ Benji: No market intelligence available for this content");
    }

    // Use the existing enhanced mention response method
    return this.generateEnhancedMentionResponse(mentionContent, context);
  }

  /**
   * Enhance context with memory from previous conversations and persona memories
   */
  private async enhanceContextWithMemory(
    content: string,
    context: BenjiContext
  ): Promise<BenjiContext> {
    if (!this.config.userId) {
      return context;
    }

    try {
      console.log(
        "🧠 Benji: Fetching memory context for user:",
        this.config.userId
      );

      // Fetch general conversation memories
      const memoryContext = await mem0Service.getUserMemoryContext(
        this.config.userId,
        content,
        3 // Limit to 3 most relevant memories
      );

      // Fetch persona-specific memories if user has a selected persona
      const personaMemoryContext = await this.getPersonaMemoryContext(content);

      const enhancedContext: BenjiContext = { ...context };
      
      if (memoryContext) {
        console.log(
          "✅ Benji: Memory context retrieved, length:",
          memoryContext.length
        );
        enhancedContext.memoryContext = memoryContext;
      } else {
        console.log("ℹ️ Benji: No relevant general memories found");
      }

      if (personaMemoryContext) {
        console.log(
          "✅ Benji: Persona memory context retrieved, length:",
          personaMemoryContext.length
        );
        enhancedContext.personaMemoryContext = personaMemoryContext;
      } else {
        console.log("ℹ️ Benji: No relevant persona memories found");
      }

      return enhancedContext;
    } catch (error) {
      console.warn("⚠️ Benji: Failed to fetch memory context:", error);
      return context; // Return original context on error
    }
  }

  /**
   * Get persona-specific memory context for the current user
   */
  private async getPersonaMemoryContext(content: string): Promise<string | null> {
    if (!this.config.userId) {
      return null;
    }

    try {
      // Get user's selected personality profile
      const user = await prisma.user.findUnique({
        where: { id: this.config.userId },
        include: {
          selectedPersonality: {
            select: {
              id: true,
              name: true,
              sourceTwitterHandle: true,
              isUserGenerated: true,
            }
          }
        }
      });

      // Only fetch persona memories if user has a generated persona selected
      if (!user?.selectedPersonality?.isUserGenerated) {
        return null;
      }

      console.log(
        `🎭 Benji: Fetching persona memories for ${user.selectedPersonality.name} (@${user.selectedPersonality.sourceTwitterHandle})`
      );

      // Search for relevant persona memories
      const personaMemories = await mem0Service.searchMemories(this.config.userId, {
        query: content,
        limit: 5,
        metadata: {
          personaId: user.selectedPersonality.id,
        }
      });

      if (personaMemories.length === 0) {
        return null;
      }

      // Format persona memories for context
      const formattedMemories = personaMemories
        .map((memory) => {
          const metadata = memory.metadata || {};
          const twitterHandle = metadata.twitterHandle || user.selectedPersonality?.sourceTwitterHandle;
          
          if (metadata.contentType === PERSONA_MEMORY_TYPES.TWEET || metadata.contentType === PERSONA_MEMORY_TYPES.REPLY) {
            return `Example ${metadata.contentType === PERSONA_MEMORY_TYPES.REPLY ? 'reply' : 'tweet'} from @${twitterHandle}: "${memory.content}"`;
          } else if (metadata.contentType === PERSONA_MEMORY_TYPES.ANALYSIS) {
            return `Analysis insight for @${twitterHandle}: ${memory.content}`;
          } else if (metadata.contentType === PERSONA_MEMORY_TYPES.PROFILE) {
            return `Persona profile: ${memory.content}`;
          }
          
          return memory.content;
        })
        .join('\n\n');

      const contextString = `## Persona Context (${user.selectedPersonality.name})
Based on analysis of @${user.selectedPersonality.sourceTwitterHandle}, here are relevant examples and insights:

${formattedMemories}

Use these examples to inform your response style and tone to match this persona.`;

      return contextString;

    } catch (error) {
      console.error("❌ Benji: Failed to fetch persona memory context:", error);
      return null;
    }
  }

  /**
   * Store conversation in memory for future reference
   */
  private async storeConversationMemory(
    content: string,
    context: BenjiContext
  ): Promise<void> {
    if (!this.config.userId) {
      return;
    }

    try {
      console.log(
        "💾 Benji: Storing conversation memory for user:",
        this.config.userId
      );

      // Format the conversation as messages
      const messages = [
        {
          role: "user",
          content: content,
        },
      ];

      // Add conversation history if available
      if (
        context.conversationHistory &&
        context.conversationHistory.length > 0
      ) {
        messages.unshift(
          ...context.conversationHistory.map((msg) => ({
            role: msg.role,
            content: msg.content,
          }))
        );
      }

      // Create conversation context for memory storage
      const conversationContext = {
        userId: this.config.userId,
        sessionId: context.sessionId || generateId(),
        mentionId: context.mentionId,
        authorInfo: context.authorInfo,
        monitoredAccountInfo: context.monitoredAccountInfo,
      };

      // Store memories with context
      await mem0Service.addMemories(
        this.config.userId,
        messages,
        conversationContext,
        {
          memoryType: "conversation",
          metadata: {
            mentionId: context.mentionId,
            authorHandle: context.authorInfo?.handle,
            monitoredAccount: context.monitoredAccountInfo?.handle,
            timestamp: new Date().toISOString(),
          },
        }
      );

      console.log("✅ Benji: Conversation memory stored successfully");
    } catch (error) {
      console.warn("⚠️ Benji: Failed to store conversation memory:", error);
      // Don't throw - this should not break the response flow
    }
  }

  /**
   * Analyze writing style from tweet content
   */
  async analyzeWritingStyle(tweets: string[]): Promise<{
    tone: string;
    formality: string;
    humor: string;
    emotionalRange: string;
    vocabulary: string;
  }> {
    const model = getModel(this.config.model!);
    const sampleText = tweets.slice(0, 20).join('\n\n'); // Analyze first 20 tweets

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert in writing style analysis. Analyze the writing style of the provided tweets and return a JSON object with these exact fields:

          {
            "tone": "professional|casual|friendly|authoritative|playful|serious",
            "formality": "very_formal|formal|semi_formal|casual|very_casual",
            "humor": "none|subtle|moderate|frequent|heavy",
            "emotionalRange": "reserved|balanced|expressive|very_expressive",
            "vocabulary": "simple|conversational|technical|academic|creative"
          }

          Base your analysis on patterns in word choice, sentence structure, emoji usage, and overall communication style.
          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the writing style of these tweets:\n\n${sampleText}`,
        },
      ],
      maxTokens: 200,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse writing style analysis:", error);
      return {
        tone: "casual",
        formality: "casual",
        humor: "moderate",
        emotionalRange: "balanced",
        vocabulary: "conversational"
      };
    }
  }

  /**
   * Extract personality traits from tweet content
   */
  async extractPersonalityTraits(tweets: string[]): Promise<{
    primary: string[];
    secondary: string[];
    communication: string[];
  }> {
    const model = getModel(this.config.model!);
    const sampleText = tweets.slice(0, 30).join('\n\n'); // Analyze first 30 tweets

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a personality analysis expert. Analyze the personality traits evident in the provided tweets and return a JSON object with these exact fields:

          {
            "primary": ["trait1", "trait2", "trait3"],
            "secondary": ["trait4", "trait5"],
            "communication": ["style1", "style2", "style3"]
          }

          Primary traits: 3 most dominant personality characteristics (e.g., "analytical", "optimistic", "helpful", "curious", "direct", "empathetic")
          Secondary traits: 2 supporting personality characteristics
          Communication traits: 3 communication style characteristics (e.g., "clear", "concise", "supportive", "questioning", "encouraging")

          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the personality traits evident in these tweets:\n\n${sampleText}`,
        },
      ],
      maxTokens: 300,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse personality traits analysis:", error);
      return {
        primary: ["helpful", "analytical", "curious"],
        secondary: ["direct", "supportive"],
        communication: ["clear", "concise", "engaging"]
      };
    }
  }

  /**
   * Identify topics of interest from tweet content
   */
  async identifyTopicsOfInterest(tweets: string[]): Promise<{
    primary: string[];
    secondary: string[];
    expertise: string[];
  }> {
    const model = getModel(this.config.model!);
    const allText = tweets.join('\n\n');

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a content analysis expert. Analyze the topics and interests evident in the provided tweets and return a JSON object with these exact fields:

          {
            "primary": ["topic1", "topic2", "topic3"],
            "secondary": ["topic4", "topic5", "topic6"],
            "expertise": ["area1", "area2"]
          }

          Primary topics: 3 most frequently discussed or passionate topics
          Secondary topics: 3-6 regularly mentioned topics
          Expertise areas: 2 areas where the person demonstrates knowledge or authority

          Focus on concrete topics like "technology", "startups", "AI", "crypto", "marketing", "design", etc.
          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Identify the topics of interest from these tweets:\n\n${allText.substring(0, 8000)}`, // Limit to avoid token limits
        },
      ],
      maxTokens: 300,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse topics analysis:", error);
      return {
        primary: ["technology", "business", "innovation"],
        secondary: ["productivity", "startups", "AI"],
        expertise: ["software", "entrepreneurship"]
      };
    }
  }

  /**
   * Analyze engagement patterns from replies and interactions
   */
  async analyzeEngagementPatterns(replies: string[]): Promise<{
    replyStyle: string;
    questionAsking: string;
    supportiveness: string;
  }> {
    const model = getModel(this.config.model!);
    const sampleReplies = replies.slice(0, 25).join('\n\n'); // Analyze first 25 replies

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an engagement pattern analyst. Analyze how this person engages with others on social media and return a JSON object with these exact fields:

          {
            "replyStyle": "brief|detailed|conversational|formal|supportive",
            "questionAsking": "rare|occasional|frequent|very_frequent",
            "supportiveness": "low|moderate|high|very_high"
          }

          Base your analysis on:
          - Reply length and depth
          - How often they ask questions
          - How supportive/encouraging they are
          - Their interaction style with others

          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the engagement patterns from these replies:\n\n${sampleReplies}`,
        },
      ],
      maxTokens: 200,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse engagement patterns analysis:", error);
      return {
        replyStyle: "conversational",
        questionAsking: "frequent",
        supportiveness: "high"
      };
    }
  }

  /**
   * Generate a comprehensive system prompt from personality analysis
   */
  async generatePersonaSystemPrompt(
    twitterHandle: string,
    analysis: {
      writingStyle: any;
      personalityTraits: any;
      topicsOfInterest: any;
      engagementPatterns: any;
    }
  ): Promise<string> {
    const model = getModel(this.config.model!);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert at creating AI personality prompts. Create a comprehensive system prompt that captures the personality and communication style of @${twitterHandle} based on their Twitter activity analysis.

          The system prompt should:
          1. Define the AI's personality and communication style
          2. Specify how they should respond to different types of content
          3. Include their areas of expertise and interests
          4. Capture their unique voice and approach
          5. Be practical for use in social media responses

          Make it detailed but concise (under 500 words). Write in second person ("You are...").`,
        },
        {
          role: "user",
          content: `Create a system prompt for @${twitterHandle} based on this analysis:

Writing Style: ${JSON.stringify(analysis.writingStyle, null, 2)}
Personality Traits: ${JSON.stringify(analysis.personalityTraits, null, 2)}
Topics of Interest: ${JSON.stringify(analysis.topicsOfInterest, null, 2)}
Engagement Patterns: ${JSON.stringify(analysis.engagementPatterns, null, 2)}`,
        },
      ],
      maxTokens: 800,
      temperature: 0.7,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    return text.trim();
  }
}

// Convenience function for quick usage
export function createBenjiAgent(config: BenjiConfig = {}) {
  return new BenjiAgent(config);
}

// Helper function to get agent for user
export async function getBenjiForUser(userId: string) {
  console.log("🤖 Benji: Getting agent for user:", userId);

  // Validate environment variables
  const requiredEnvVars = ["OPENROUTER_API_KEY"];
  const missingVars = requiredEnvVars.filter((key) => !process.env[key]);
  if (missingVars.length > 0) {
    console.error("❌ Benji: Missing environment variables:", missingVars);
    throw new Error(
      `Missing required environment variables: ${missingVars.join(", ")}`
    );
  }

  // Get user's plan, personality, and selected model from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: true,
      selectedPersonality: true,
      selectedModel: true,
    },
  });

  if (!user) {
    console.error("❌ Benji: User not found:", userId);
    throw new Error("User not found");
  }

  console.log("✅ Benji: User found with plan:", user.plan.name);
  console.log(
    "🎭 Benji: Personality:",
    user.selectedPersonality?.name || "None"
  );
  console.log(
    "🧠 Benji: Selected model:",
    user.selectedModel?.name || "Plan default"
  );
  console.log(
    "📝 Benji: Custom prompt:",
    user.customSystemPrompt ? "Yes" : "No"
  );
  console.log("👤 Benji: First person mode:", user.useFirstPerson ?? true);
  console.log("🔑 Benji: Environment check passed");

  // Determine model to use: user's selected model or plan default
  let modelToUse: ModelName | undefined;
  if (user.selectedModel) {
    // Map database model names to ModelName enum
    const modelNameMap: Record<string, ModelName> = {
      Workhorse: "gemini25Flash",
      Smarty: "gemini25Pro",
      "Big Brain": "openaiO3",
    };
    modelToUse = modelNameMap[user.selectedModel.name];
  }

  return new BenjiAgent({
    userId,
    userPlan: user.plan.name,
    model: modelToUse, // Use selected model or fallback to plan default
    enableTools: true,
    personalityPrompt: user.selectedPersonality?.systemPrompt,
    customSystemPrompt: user.customSystemPrompt || undefined,
    useFirstPerson: user.useFirstPerson ?? true,
  });
}
