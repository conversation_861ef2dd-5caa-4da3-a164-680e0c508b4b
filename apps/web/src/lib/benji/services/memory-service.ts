/**
 * Memory Service - Conversation and persona memory management
 * 
 * Handles storing, retrieving, and managing conversation memories
 * and persona-specific memories using Mem0.
 */

import { generateId } from "ai";
import { prisma } from "../../db-utils";
import mem0Service from "../../mem0-service";
import { PERSONA_MEMORY_TYPES } from "../../twitter-persona-service";
import type { BenjiContext, ConversationContext, MemoryOptions } from "../types";

export class MemoryService {
  /**
   * Enhance context with memory from previous conversations and persona memories
   */
  async enhanceContextWithMemory(
    content: string,
    context: BenjiContext,
    userId?: string
  ): Promise<BenjiContext> {
    if (!userId) {
      return context;
    }

    try {
      console.log("🧠 MemoryService: Fetching memory context for user:", userId);

      // Fetch general conversation memories
      const memoryContext = await this.getUserMemoryContext(userId, content);

      // Fetch persona-specific memories if user has a selected persona
      const personaMemoryContext = await this.getPersonaMemoryContext(userId, content);

      const enhancedContext: BenjiContext = { ...context };
      
      if (memoryContext) {
        console.log(
          "✅ MemoryService: Memory context retrieved, length:",
          memoryContext.length
        );
        enhancedContext.memoryContext = memoryContext;
      } else {
        console.log("ℹ️ MemoryService: No relevant general memories found");
      }

      if (personaMemoryContext) {
        console.log(
          "✅ MemoryService: Persona memory context retrieved, length:",
          personaMemoryContext.length
        );
        enhancedContext.personaMemoryContext = personaMemoryContext;
      } else {
        console.log("ℹ️ MemoryService: No relevant persona memories found");
      }

      return enhancedContext;
    } catch (error) {
      console.warn("⚠️ MemoryService: Failed to fetch memory context:", error);
      return context; // Return original context on error
    }
  }

  /**
   * Get user's general conversation memory context
   */
  private async getUserMemoryContext(
    userId: string,
    content: string,
    limit: number = 3
  ): Promise<string | null> {
    try {
      return await mem0Service.getUserMemoryContext(userId, content, limit);
    } catch (error) {
      console.error("❌ MemoryService: Failed to fetch user memory context:", error);
      return null;
    }
  }

  /**
   * Get persona-specific memory context for the current user
   */
  private async getPersonaMemoryContext(
    userId: string,
    content: string
  ): Promise<string | null> {
    try {
      // Get user's selected personality profile
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          selectedPersonality: {
            select: {
              id: true,
              name: true,
              sourceTwitterHandle: true,
              isUserGenerated: true,
            }
          }
        }
      });

      // Only fetch persona memories if user has a generated persona selected
      if (!user?.selectedPersonality?.isUserGenerated) {
        return null;
      }

      console.log(
        `🎭 MemoryService: Fetching persona memories for ${user.selectedPersonality.name} (@${user.selectedPersonality.sourceTwitterHandle})`
      );

      // Search for relevant persona memories
      const personaMemories = await mem0Service.searchMemories(userId, {
        query: content,
        limit: 5,
        metadata: {
          personaId: user.selectedPersonality.id,
        }
      });

      if (personaMemories.length === 0) {
        return null;
      }

      // Format persona memories for context
      const formattedMemories = personaMemories
        .map((memory) => {
          const metadata = memory.metadata || {};
          const twitterHandle = metadata.twitterHandle || user.selectedPersonality?.sourceTwitterHandle;
          
          if (metadata.contentType === PERSONA_MEMORY_TYPES.TWEET || metadata.contentType === PERSONA_MEMORY_TYPES.REPLY) {
            return `Example ${metadata.contentType === PERSONA_MEMORY_TYPES.REPLY ? 'reply' : 'tweet'} from @${twitterHandle}: "${memory.content}"`;
          } else if (metadata.contentType === PERSONA_MEMORY_TYPES.ANALYSIS) {
            return `Analysis insight for @${twitterHandle}: ${memory.content}`;
          } else if (metadata.contentType === PERSONA_MEMORY_TYPES.PROFILE) {
            return `Persona profile: ${memory.content}`;
          }
          
          return memory.content;
        })
        .join('\n\n');

      const contextString = `## Persona Context (${user.selectedPersonality.name})
Based on analysis of @${user.selectedPersonality.sourceTwitterHandle}, here are relevant examples and insights:

${formattedMemories}

Use these examples to inform your response style and tone to match this persona.`;

      return contextString;

    } catch (error) {
      console.error("❌ MemoryService: Failed to fetch persona memory context:", error);
      return null;
    }
  }

  /**
   * Store conversation in memory for future reference
   */
  async storeConversationMemory(
    content: string,
    context: BenjiContext,
    userId?: string
  ): Promise<void> {
    if (!userId) {
      return;
    }

    try {
      console.log("💾 MemoryService: Storing conversation memory for user:", userId);

      // Format the conversation as messages
      const messages = [
        {
          role: "user",
          content: content,
        },
      ];

      // Add conversation history if available
      if (
        context.conversationHistory &&
        context.conversationHistory.length > 0
      ) {
        messages.unshift(
          ...context.conversationHistory.map((msg) => ({
            role: msg.role,
            content: msg.content,
          }))
        );
      }

      // Create conversation context for memory storage
      const conversationContext: ConversationContext = {
        userId,
        sessionId: context.sessionId || generateId(),
        mentionId: context.mentionId,
        authorInfo: context.authorInfo,
        monitoredAccountInfo: context.monitoredAccountInfo,
      };

      // Memory options
      const memoryOptions: MemoryOptions = {
        memoryType: "conversation",
        metadata: {
          mentionId: context.mentionId,
          authorHandle: context.authorInfo?.handle,
          monitoredAccount: context.monitoredAccountInfo?.handle,
          timestamp: new Date().toISOString(),
        },
      };

      // Store memories with context
      await mem0Service.addMemories(
        userId,
        messages,
        conversationContext,
        memoryOptions
      );

      console.log("✅ MemoryService: Conversation memory stored successfully");
    } catch (error) {
      console.warn("⚠️ MemoryService: Failed to store conversation memory:", error);
      // Don't throw - this should not break the response flow
    }
  }

  /**
   * Search for specific memories
   */
  async searchMemories(
    userId: string,
    query: string,
    options?: {
      limit?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<any[]> {
    try {
      return await mem0Service.searchMemories(userId, {
        query,
        limit: options?.limit || 10,
        metadata: options?.metadata,
      });
    } catch (error) {
      console.error("❌ MemoryService: Failed to search memories:", error);
      return [];
    }
  }

  /**
   * Delete specific memories
   */
  async deleteMemory(userId: string, memoryId: string): Promise<boolean> {
    try {
      await mem0Service.deleteMemory(memoryId);
      console.log(`✅ MemoryService: Deleted memory ${memoryId} for user ${userId}`);
      return true;
    } catch (error) {
      console.error("❌ MemoryService: Failed to delete memory:", error);
      return false;
    }
  }

  /**
   * Clear all memories for a user
   */
  async clearUserMemories(userId: string): Promise<boolean> {
    try {
      // Search for all memories
      const memories = await this.searchMemories(userId, "", { limit: 100 });
      
      // Delete each memory
      const deletePromises = memories.map((memory) => 
        this.deleteMemory(userId, memory.id)
      );
      
      await Promise.all(deletePromises);
      
      console.log(`✅ MemoryService: Cleared ${memories.length} memories for user ${userId}`);
      return true;
    } catch (error) {
      console.error("❌ MemoryService: Failed to clear user memories:", error);
      return false;
    }
  }
}