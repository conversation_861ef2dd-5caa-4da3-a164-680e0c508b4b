/**
 * Market Intelligence Service - Crypto market data and analysis
 * 
 * Fetches and caches market intelligence data from Cookie.fun API,
 * detects crypto-related content, and provides market context.
 */

import { cookieClient } from "../../cookie-client";
import type { MarketIntelligence, CacheEntry } from "../types";

export class MarketIntelligenceService {
  private cache: Map<string, CacheEntry<MarketIntelligence | null>> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

  // Comprehensive list of crypto-related keywords
  private readonly cryptoKeywords = [
    "bitcoin", "btc", "ethereum", "eth", "crypto", "blockchain", "defi", "nft",
    "web3", "dao", "token", "coin", "altcoin", "trading", "hodl", "mining",
    "staking", "yield", "liquidity", "dex", "cex", "smart contract", "protocol",
    "layer", "chain", "wallet", "bull", "bear", "moon", "diamond hands", "ape",
    "solana", "sol", "cardano", "ada", "polkadot", "dot", "chainlink", "link",
    "uniswap", "uni", "aave", "compound", "maker", "mkr", "sushi", "pancake",
    "binance", "bnb", "polygon", "matic", "avalanche", "avax", "fantom", "ftm",
    "cosmos", "atom", "terra", "luna", "near", "flow", "tezos", "xtz",
    "algorand", "algo", "hedera", "hbar", "icp", "dfinity", "theta", "tfuel",
    "gala", "sand", "mana", "axs", "slp", "enjin", "enj", "chz", "chiliz",
  ];

  // Sector detection keywords
  private readonly sectorKeywords: Record<string, string[]> = {
    defi: [
      "defi", "decentralized finance", "yield", "liquidity", "dex",
      "lending", "borrowing", "swap", "farm", "pool",
    ],
    gaming: [
      "gaming", "play to earn", "p2e", "nft game", "metaverse",
      "virtual world", "gamefi", "guild",
    ],
    infrastructure: [
      "infrastructure", "layer 1", "layer 2", "blockchain", "protocol",
      "consensus", "validator", "node",
    ],
    meme: [
      "meme", "doge", "shib", "pepe", "moon", "ape",
      "diamond hands", "hodl", "to the moon",
    ],
    ai: [
      "ai", "artificial intelligence", "machine learning", "neural",
      "compute", "gpu", "training",
    ],
    layer1: [
      "layer 1", "l1", "mainnet", "consensus", "validator", "staking",
    ],
    layer2: [
      "layer 2", "l2", "scaling", "rollup", "sidechain", "plasma",
    ],
  };

  /**
   * Fetch market intelligence for crypto-related content
   */
  async fetchMarketIntelligence(
    content: string
  ): Promise<MarketIntelligence | null> {
    try {
      // Create cache key based on content hash
      const cacheKey = this.createCacheKey(content);

      // Check cache first
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        console.log("🍪 MarketIntelligence: Using cached data");
        return cached.data;
      }

      // Detect if content is crypto-related
      if (!this.isContentCryptoRelated(content)) {
        console.log(
          "🍪 MarketIntelligence: No crypto keywords detected, skipping"
        );
        return null;
      }

      console.log(
        "🍪 MarketIntelligence: Detected crypto-related content, fetching data..."
      );

      // Extract project mentions and detect sector
      const projectSlugs = this.extractProjectMentions(content);
      const detectedSector = this.detectSector(content);

      console.log("🔍 MarketIntelligence: Detected project mentions:", projectSlugs);
      console.log("🎯 MarketIntelligence: Detected sector:", detectedSector || "general");

      // Fetch market data
      const { trendingProjects, relevantProjects } = await this.fetchProjectData(
        projectSlugs,
        detectedSector
      );

      // Determine market sentiment
      const marketSentiment = this.calculateMarketSentiment(trendingProjects);

      const marketIntelligence: MarketIntelligence = {
        trendingProjects,
        relevantProjects,
        sector: detectedSector,
        marketSentiment,
        updatedAt: new Date().toISOString(),
      };

      // Cache the result
      this.cacheResult(cacheKey, marketIntelligence);

      console.log("✅ MarketIntelligence: Data fetched and cached");
      return marketIntelligence;
    } catch (error) {
      console.warn(
        "⚠️ MarketIntelligence: Failed to fetch data, continuing without it:",
        error
      );
      return null;
    }
  }

  /**
   * Check if content contains crypto-related keywords
   */
  private isContentCryptoRelated(content: string): boolean {
    const contentLower = content.toLowerCase();
    return this.cryptoKeywords.some(
      (keyword) =>
        contentLower.includes(keyword) || contentLower.includes(`$${keyword}`)
    );
  }

  /**
   * Extract project mentions from content
   */
  private extractProjectMentions(content: string): string[] {
    const contentLower = content.toLowerCase();
    const projectSlugs: string[] = [];

    // Extract $SYMBOL patterns
    const symbolMatches = content.match(/\$[A-Z]{2,10}/g);
    if (symbolMatches) {
      projectSlugs.push(
        ...symbolMatches.map((symbol) => symbol.substring(1).toLowerCase())
      );
    }

    // Common project names
    const commonProjects = [
      "bitcoin", "ethereum", "solana", "cardano", "polkadot",
      "chainlink", "uniswap", "aave",
    ];
    
    const mentionedProjects = commonProjects.filter((project) =>
      contentLower.includes(project)
    );
    projectSlugs.push(...mentionedProjects);

    // Remove duplicates
    return [...new Set(projectSlugs)];
  }

  /**
   * Detect sector from content keywords
   */
  private detectSector(content: string): string | undefined {
    const contentLower = content.toLowerCase();

    for (const [sector, keywords] of Object.entries(this.sectorKeywords)) {
      if (keywords.some((keyword) => contentLower.includes(keyword))) {
        return sector;
      }
    }

    return undefined;
  }

  /**
   * Fetch project data from Cookie.fun API
   */
  private async fetchProjectData(
    projectSlugs: string[],
    detectedSector?: string
  ): Promise<{
    trendingProjects: MarketIntelligence["trendingProjects"];
    relevantProjects: MarketIntelligence["relevantProjects"];
  }> {
    let trendingProjects: any[] = [];
    let relevantProjects: any[] = [];

    try {
      // Get trending projects in detected sector or general trending
      console.log(
        "📊 MarketIntelligence: Fetching trending projects for sector:",
        detectedSector || "general"
      );

      const trending = await cookieClient.getTrendingProjects(
        detectedSector,
        "_7Days"
      );
      
      trendingProjects = trending.slice(0, 5).map((project) => ({
        name: project.name,
        symbol: project.symbol,
        sector: project.sector,
        mindshare: project.mindshare,
        mindshareDelta: project.mindshareDelta,
      }));

      console.log(
        "✅ MarketIntelligence: Fetched trending projects:",
        trendingProjects.length
      );

      // Fetch specific project data if mentions found
      if (projectSlugs.length > 0) {
        console.log(
          "🔍 MarketIntelligence: Processing specific project mentions:",
          projectSlugs
        );

        try {
          const projectSearchPromises = projectSlugs
            .slice(0, 3) // Limit to 3 projects
            .map(async (slug) => {
              try {
                const searchResult = await cookieClient.searchProjects({
                  query: slug,
                  limit: 1,
                });
                
                if (searchResult.data && searchResult.data.length > 0) {
                  const project = searchResult.data[0];
                  return {
                    name: project.name,
                    symbol: project.symbol,
                    sector: project.sector,
                    mindshare: project.mindshare,
                    mindshareDelta: project.mindshareDelta,
                  };
                }
                return null;
              } catch (searchError) {
                console.warn(
                  `⚠️ MarketIntelligence: Failed to fetch data for project ${slug}:`,
                  searchError
                );
                return null;
              }
            });

          const projectResults = await Promise.all(projectSearchPromises);
          relevantProjects = projectResults.filter(Boolean);

          console.log(
            "✅ MarketIntelligence: Fetched relevant project data:",
            relevantProjects.length
          );
        } catch (projectError) {
          console.warn(
            "⚠️ MarketIntelligence: Failed to fetch specific project data, using fallback:",
            projectError
          );

          // Fallback to basic project info
          relevantProjects = projectSlugs.slice(0, 3).map((slug) => ({
            name: slug,
            symbol: slug.toUpperCase(),
            sector: detectedSector || "crypto",
            mindshare: 50,
            mindshareDelta: 0,
          }));
        }
      }
    } catch (error) {
      console.warn(
        "⚠️ MarketIntelligence: Failed to fetch market data:",
        error
      );
    }

    return { trendingProjects, relevantProjects };
  }

  /**
   * Calculate market sentiment based on trending projects
   */
  private calculateMarketSentiment(
    trendingProjects: any[]
  ): "bullish" | "bearish" | "neutral" {
    if (trendingProjects.length === 0) return "neutral";

    const avgMindshare =
      trendingProjects.reduce((sum, p) => sum + (p.mindshare || 0), 0) /
      trendingProjects.length;

    if (avgMindshare > 70) return "bullish";
    if (avgMindshare < 30) return "bearish";
    return "neutral";
  }

  /**
   * Create cache key from content
   */
  private createCacheKey(content: string): string {
    return Buffer.from(content.toLowerCase().replace(/\s+/g, " ").trim())
      .toString("base64")
      .slice(0, 32);
  }

  /**
   * Cache result and clean old entries
   */
  private cacheResult(key: string, data: MarketIntelligence | null): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean old cache entries (keep only last 10)
    if (this.cache.size > 10) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) this.cache.delete(oldestKey);
    }
  }

  /**
   * Clear cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.cache.clear();
  }
}