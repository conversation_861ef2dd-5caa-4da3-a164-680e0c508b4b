/**
 * OpenAI Streaming Image Generation Tool
 *
 * Provides streaming image generation with partial images for better UX
 * Uses OpenAI's gpt-image-1 model via Responses API
 */

import { tool } from "ai";
import { z } from "zod";

export const streamingImageGenerationTool = tool({
  description: "Generate images with streaming partial results using OpenAI's gpt-image-1 model",
  parameters: z.object({
    prompt: z.string().describe("Detailed image generation prompt"),
    size: z
      .enum(["1024x1024", "1792x1024", "1024x1792", "1536x1024", "1024x1536"])
      .optional()
      .default("auto")
      .describe("Image dimensions - use 'auto' for automatic selection"),
    quality: z
      .enum(["low", "medium", "high", "auto"])
      .optional()
      .default("auto")
      .describe("Rendering quality - use 'auto' for automatic selection"),
    format: z
      .enum(["png", "jpeg", "webp"])
      .optional()
      .default("png")
      .describe("Output image format"),
    partialImages: z
      .number()
      .min(1)
      .max(3)
      .optional()
      .default(2)
      .describe("Number of partial images to stream (1-3)"),
    onPartialImage: z
      .function()
      .optional()
      .describe("Callback function for partial image updates"),
  }),
  execute: async ({ 
    prompt, 
    size, 
    quality, 
    format, 
    partialImages,
    onPartialImage 
  }) => {
    try {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY not configured");
      }

      console.log("🎨 OpenAI Streaming: Starting streaming image generation");
      console.log("📝 OpenAI Streaming: Prompt:", prompt.substring(0, 100) + "...");

      // Create OpenAI client
      const OpenAI = (await import("openai")).default;
      const client = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Configure streaming image generation
      const imageGenTool = {
        type: "image_generation" as const,
        partial_images: partialImages,
        ...(size !== "auto" && { size }),
        ...(quality !== "auto" && { quality }),
        ...(format && { format }),
      };

      console.log("🔧 OpenAI Streaming: Starting stream with config:", imageGenTool);

      // Create streaming response
      const stream = await client.responses.create({
        model: "gpt-4o-mini",
        input: `Generate an image: ${prompt}`,
        stream: true,
        tools: [imageGenTool],
        tool_choice: { type: "image_generation" },
      });

      const partialImages: string[] = [];
      let finalImageBase64: string | null = null;
      let revisedPrompt: string | null = null;
      let responseId: string | null = null;
      let imageCallId: string | null = null;

      // Process streaming events
      for await (const event of stream) {
        console.log("📡 OpenAI Streaming: Event type:", event.type);

        if (event.type === "response.image_generation_call.partial_image") {
          const idx = event.partial_image_index;
          const imageBase64 = event.partial_image_b64;
          
          console.log(`🖼️ OpenAI Streaming: Received partial image ${idx}`);
          
          partialImages[idx] = imageBase64;
          
          // Call callback if provided
          if (onPartialImage) {
            const imageDataUrl = `data:image/${format};base64,${imageBase64}`;
            onPartialImage({
              index: idx,
              imageBase64,
              imageDataUrl,
              isPartial: true,
            });
          }
        } else if (event.type === "response.image_generation_call.done") {
          console.log("✅ OpenAI Streaming: Image generation completed");
          
          finalImageBase64 = event.image_generation_call.result;
          revisedPrompt = event.image_generation_call.revised_prompt;
          imageCallId = event.image_generation_call.id;
        } else if (event.type === "response.done") {
          console.log("🏁 OpenAI Streaming: Stream completed");
          responseId = event.response.id;
        }
      }

      if (!finalImageBase64) {
        throw new Error("No final image received from streaming response");
      }

      console.log("🎉 OpenAI Streaming: Final image generated successfully");
      console.log("📝 OpenAI Streaming: Revised prompt:", revisedPrompt);

      // Convert base64 to data URL
      const imageDataUrl = `data:image/${format};base64,${finalImageBase64}`;

      // Call final callback if provided
      if (onPartialImage) {
        onPartialImage({
          index: -1, // -1 indicates final image
          imageBase64: finalImageBase64,
          imageDataUrl,
          isPartial: false,
        });
      }

      return {
        imageUrl: imageDataUrl,
        imageBase64: finalImageBase64,
        originalPrompt: prompt,
        revisedPrompt,
        size: size === "auto" ? "auto-selected" : size,
        quality: quality === "auto" ? "auto-selected" : quality,
        format,
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1 (Streaming)",
        model: "gpt-image-1",
        responseId,
        imageCallId,
        partialImages,
        streamingEnabled: true,
        // TODO: Add UploadThing metadata
        uploadThing: {
          url: imageDataUrl,
          key: null, // Would be set after UploadThing upload
        },
      };
    } catch (error) {
      console.error("❌ OpenAI Streaming: Generation error:", error);

      // Return graceful error response
      return {
        originalPrompt: prompt,
        error:
          error instanceof Error ? error.message : "Failed to generate streaming image",
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1 (Streaming)",
        model: "gpt-image-1",
        streamingEnabled: true,
      };
    }
  },
});

/**
 * Multi-turn Image Editing Tool
 * 
 * Allows iterative editing of previously generated images
 */
export const multiTurnImageEditTool = tool({
  description: "Edit previously generated images using conversation context",
  parameters: z.object({
    editPrompt: z.string().describe("Description of how to edit the image"),
    previousResponseId: z.string().optional().describe("ID of previous response to reference"),
    previousImageCallId: z.string().optional().describe("ID of previous image generation call"),
    size: z
      .enum(["1024x1024", "1792x1024", "1024x1792", "1536x1024", "1024x1536"])
      .optional()
      .default("auto"),
    quality: z
      .enum(["low", "medium", "high", "auto"])
      .optional()
      .default("auto"),
    format: z
      .enum(["png", "jpeg", "webp"])
      .optional()
      .default("png"),
  }),
  execute: async ({ 
    editPrompt, 
    previousResponseId, 
    previousImageCallId,
    size, 
    quality, 
    format 
  }) => {
    try {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY not configured");
      }

      console.log("✏️ OpenAI Multi-turn: Starting image edit");
      console.log("📝 OpenAI Multi-turn: Edit prompt:", editPrompt);

      // Create OpenAI client
      const OpenAI = (await import("openai")).default;
      const client = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Configure image generation tool
      const imageGenTool = {
        type: "image_generation" as const,
        ...(size !== "auto" && { size }),
        ...(quality !== "auto" && { quality }),
        ...(format && { format }),
      };

      let input: any;

      if (previousResponseId) {
        // Use previous response ID for context
        input = editPrompt;
        console.log("🔗 OpenAI Multi-turn: Using previous response ID:", previousResponseId);
      } else if (previousImageCallId) {
        // Use previous image call ID
        input = [
          {
            role: "user",
            content: [{ type: "input_text", text: editPrompt }],
          },
          {
            type: "image_generation_call",
            id: previousImageCallId,
          },
        ];
        console.log("🔗 OpenAI Multi-turn: Using previous image call ID:", previousImageCallId);
      } else {
        // No previous context, treat as new generation
        input = `Edit this image: ${editPrompt}`;
        console.log("🆕 OpenAI Multi-turn: No previous context, creating new image");
      }

      // Generate edited image
      const response = await client.responses.create({
        model: "gpt-4o-mini",
        input,
        ...(previousResponseId && { previous_response_id: previousResponseId }),
        tools: [imageGenTool],
        tool_choice: { type: "image_generation" },
      });

      // Extract image generation results
      const imageGenerationCalls = response.output.filter(
        (output: any) => output.type === "image_generation_call"
      );

      if (imageGenerationCalls.length === 0) {
        throw new Error("No image generation call found in edit response");
      }

      const imageCall = imageGenerationCalls[0];
      
      if (imageCall.status !== "completed") {
        throw new Error(`Image edit failed with status: ${imageCall.status}`);
      }

      const imageBase64 = imageCall.result;
      const revisedPrompt = imageCall.revised_prompt;

      console.log("✅ OpenAI Multi-turn: Image edited successfully");
      console.log("📝 OpenAI Multi-turn: Revised prompt:", revisedPrompt);

      // Convert base64 to data URL
      const imageDataUrl = `data:image/${format};base64,${imageBase64}`;

      return {
        imageUrl: imageDataUrl,
        imageBase64,
        originalPrompt: editPrompt,
        revisedPrompt,
        size: size === "auto" ? "auto-selected" : size,
        quality: quality === "auto" ? "auto-selected" : quality,
        format,
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1 (Multi-turn)",
        model: "gpt-image-1",
        responseId: response.id,
        imageCallId: imageCall.id,
        previousResponseId,
        previousImageCallId,
        isEdit: true,
        // TODO: Add UploadThing metadata
        uploadThing: {
          url: imageDataUrl,
          key: null,
        },
      };
    } catch (error) {
      console.error("❌ OpenAI Multi-turn: Edit error:", error);

      return {
        originalPrompt: editPrompt,
        error:
          error instanceof Error ? error.message : "Failed to edit image",
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1 (Multi-turn)",
        model: "gpt-image-1",
        isEdit: true,
      };
    }
  },
});
