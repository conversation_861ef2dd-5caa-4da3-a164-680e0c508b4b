/**
 * Test for upgraded OpenAI image generation with gpt-image-1 model
 */

import { describe, it, expect, beforeAll } from 'vitest';
import { imageGenerationTool } from './lib/tools/openai-image';
import { streamingImageGenerationTool, multiTurnImageEditTool } from './lib/tools/openai-image-streaming';

describe('OpenAI Image Generation Upgrade', () => {
  beforeAll(() => {
    // Ensure OpenAI API key is available for testing
    if (!process.env.OPENAI_API_KEY) {
      console.warn('⚠️ OPENAI_API_KEY not set - image generation tests will be skipped');
    }
  });

  describe('Basic Image Generation Tool', () => {
    it('should have correct tool configuration', () => {
      expect(imageGenerationTool.description).toContain('gpt-image-1');
      expect(imageGenerationTool.parameters).toBeDefined();
      
      // Check that parameters include new options
      const schema = imageGenerationTool.parameters;
      expect(schema.shape.prompt).toBeDefined();
      expect(schema.shape.size).toBeDefined();
      expect(schema.shape.quality).toBeDefined();
      expect(schema.shape.format).toBeDefined();
      expect(schema.shape.background).toBeDefined();
    });

    it('should support auto settings for size, quality, and background', () => {
      const schema = imageGenerationTool.parameters;
      
      // Check that 'auto' is included in enum options
      expect(schema.shape.size._def.values).toContain('auto');
      expect(schema.shape.quality._def.values).toContain('auto');
      expect(schema.shape.background._def.values).toContain('auto');
    });

    it('should have proper default values', () => {
      const schema = imageGenerationTool.parameters;
      
      expect(schema.shape.size._def.defaultValue()).toBe('auto');
      expect(schema.shape.quality._def.defaultValue()).toBe('auto');
      expect(schema.shape.background._def.defaultValue()).toBe('auto');
      expect(schema.shape.format._def.defaultValue()).toBe('png');
    });
  });

  describe('Streaming Image Generation Tool', () => {
    it('should have streaming-specific configuration', () => {
      expect(streamingImageGenerationTool.description).toContain('streaming');
      expect(streamingImageGenerationTool.description).toContain('gpt-image-1');
      
      const schema = streamingImageGenerationTool.parameters;
      expect(schema.shape.partialImages).toBeDefined();
      expect(schema.shape.onPartialImage).toBeDefined();
    });

    it('should support 1-3 partial images', () => {
      const schema = streamingImageGenerationTool.parameters;
      const partialImagesSchema = schema.shape.partialImages;
      
      expect(partialImagesSchema._def.minValue).toBe(1);
      expect(partialImagesSchema._def.maxValue).toBe(3);
      expect(partialImagesSchema._def.defaultValue()).toBe(2);
    });
  });

  describe('Multi-turn Image Editing Tool', () => {
    it('should have editing-specific configuration', () => {
      expect(multiTurnImageEditTool.description).toContain('edit');
      expect(multiTurnImageEditTool.description).toContain('conversation context');
      
      const schema = multiTurnImageEditTool.parameters;
      expect(schema.shape.editPrompt).toBeDefined();
      expect(schema.shape.previousResponseId).toBeDefined();
      expect(schema.shape.previousImageCallId).toBeDefined();
    });

    it('should support optional previous context', () => {
      const schema = multiTurnImageEditTool.parameters;
      
      expect(schema.shape.previousResponseId.isOptional()).toBe(true);
      expect(schema.shape.previousImageCallId.isOptional()).toBe(true);
    });
  });

  describe('Tool Integration', () => {
    it('should handle missing OpenAI API key gracefully', async () => {
      // Temporarily remove API key
      const originalKey = process.env.OPENAI_API_KEY;
      delete process.env.OPENAI_API_KEY;

      try {
        const result = await imageGenerationTool.execute({
          prompt: 'Test image generation',
        });

        expect(result.error).toContain('OPENAI_API_KEY not configured');
        expect(result.source).toBe('OpenAI gpt-image-1');
      } finally {
        // Restore API key
        if (originalKey) {
          process.env.OPENAI_API_KEY = originalKey;
        }
      }
    });

    it('should return proper error structure on failure', async () => {
      // Test with invalid configuration
      const result = await imageGenerationTool.execute({
        prompt: '', // Empty prompt should cause error
      });

      if (result.error) {
        expect(result.originalPrompt).toBe('');
        expect(result.timestamp).toBeDefined();
        expect(result.source).toBe('OpenAI gpt-image-1');
        expect(result.model).toBe('gpt-image-1');
      }
    });
  });

  describe('Upgrade Verification', () => {
    it('should use gpt-image-1 model instead of dall-e-3', () => {
      // Verify that the tools reference the new model
      expect(imageGenerationTool.description).toContain('gpt-image-1');
      expect(streamingImageGenerationTool.description).toContain('gpt-image-1');
      expect(multiTurnImageEditTool.description).toContain('conversation context');
    });

    it('should support new features not available in DALL-E 3', () => {
      const schema = imageGenerationTool.parameters;
      
      // New features in gpt-image-1
      expect(schema.shape.background).toBeDefined(); // Background control
      expect(schema.shape.format).toBeDefined(); // Format options
      
      // Auto optimization
      expect(schema.shape.size._def.values).toContain('auto');
      expect(schema.shape.quality._def.values).toContain('auto');
    });

    it('should provide base64 output instead of URLs only', async () => {
      // Mock a successful response
      const mockResult = {
        imageBase64: 'mock-base64-data',
        imageUrl: 'data:image/png;base64,mock-base64-data',
      };

      expect(mockResult.imageBase64).toBeDefined();
      expect(mockResult.imageUrl).toContain('data:image/');
    });
  });
});
